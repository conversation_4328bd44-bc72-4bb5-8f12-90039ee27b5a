:root { --primary-color: #366092; --secondary-color: #D9E2F3; --input-color: #FFF2CC; --text-color: #333; --light-text: #FFFFFF; --border-color: #B2B2B2; --card-bg: #FFFFFF; --body-bg: #f4f7f6; --shadow: 0 4px 8px rgba(0,0,0,0.1); }
body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 0; background-color: var(--body-bg); color: var(--text-color); line-height: 1.6; }
.container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
.navbar { background-color: var(--primary-color); color: var(--light-text); padding: 1rem 0; box-shadow: var(--shadow); }
.navbar .container { display: flex; justify-content: space-between; align-items: center; }
.nav-brand { font-size: 1.5rem; font-weight: bold; color: var(--light-text); text-decoration: none; }
.nav-links { list-style: none; margin: 0; padding: 0; display: flex; }
.nav-links li a { color: var(--light-text); text-decoration: none; padding: 0.5rem 1rem; transition: background-color 0.2s; border-radius: 4px; }
.nav-links li a:hover { background-color: rgba(255,255,255,0.1); }
main { padding: 2rem 0; }
.content-block { text-align: center; margin-bottom: 2rem; }
h1 { color: var(--primary-color); }
.calculator-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; }
.card { background-color: var(--card-bg); border-radius: 8px; padding: 2rem; box-shadow: var(--shadow); }
.card h2 { margin-top: 0; color: var(--primary-color); border-bottom: 2px solid var(--secondary-color); padding-bottom: 0.5rem; }
.placeholder { text-align: center; color: #777; border: 2px dashed var(--border-color); }
.form-group { margin-bottom: 1rem; }
.form-group label { display: block; font-weight: bold; margin-bottom: 0.5rem; }
.form-group input, .form-group select { width: 100%; padding: 0.8rem; border: 1px solid var(--border-color); border-radius: 4px; background-color: var(--input-color); font-size: 1rem; box-sizing: border-box;}
.btn { display: block; width: 100%; padding: 1rem; border: none; border-radius: 4px; background-color: var(--primary-color); color: var(--light-text); font-size: 1.2rem; font-weight: bold; cursor: pointer; }
.results-summary { text-align: center; margin-bottom: 2rem; }
.final-price { font-size: 2.5rem; font-weight: bold; color: #E2EFDA; background-color: var(--primary-color); padding: 0.5rem 1.5rem; border-radius: 8px; }
.details-grid { display: grid; gap: 0.5rem; }
.detail-item { display: flex; justify-content: space-between; padding: 0.5rem; border-radius: 4px; }
.detail-item:nth-child(odd) { background-color: #f9f9f9; }
.detail-item .label { font-weight: bold; }
.settings-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
th, td { padding: 0.8rem; text-align: left; border-bottom: 1px solid var(--secondary-color); }
th { background-color: var(--primary-color); color: var(--light-text); }
tbody tr:nth-child(even) { background-color: var(--secondary-color); }
.alert { padding: 1rem; margin-bottom: 1rem; border-radius: 4px; color: var(--light-text); }
.alert-success { background-color: #28a745; }
.alert-danger { background-color: #dc3545; }
footer { text-align: center; padding: 2rem 0; margin-top: 2rem; background-color: #e9ecef; color: #6c757d; }