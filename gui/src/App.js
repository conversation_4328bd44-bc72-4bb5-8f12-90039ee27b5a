import logo from './logo.svg';
import './App.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-switch-button-react/src/style.css';

import React, { useState, useEffect } from 'react';
import SwitchButton from 'bootstrap-switch-button-react';

function App() {
  const [logs, setLogs] = useState('');
  // Define predefined combinations based on documentation analysis
  const [combinations, setCombinations] = useState([
    { name: 'webui-ollama', enabled: false },
    { name: 'webui-tts', enabled: false },
    // Add more combinations as needed based on your harbor setup
  ]);

  // Function to handle toggle change for a combination
  const handleCombinationToggle = async (combinationName, isOn) => {
    const action = isOn ? 'on' : 'off';
    console.log(`${action} combination: ${combinationName}`);

    try {
      const response = await fetch('/api/combination', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ combination_name: combinationName, action: action }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`Combination ${combinationName} turned ${action} successfully:`, result.message);
        // Update the state to reflect the new toggle state
        setCombinations(combinations.map(combo =>
          combo.name === combinationName ? { ...combo, enabled: isOn } : combo
        ));
        // Optionally fetch logs related to this combination if applicable
        // fetchLogsForCombination(combinationName);
      } else {
        const errorData = await response.json();
        console.error(`Failed to turn ${action} combination ${combinationName}:`, errorData.error);
        // Revert the toggle state if the action failed
        setCombinations(combinations.map(combo =>
          combo.name === combinationName ? { ...combo, enabled: !isOn } : combo
        ));
      }
    } catch (error) {
      console.error('Error:', error);
      // Revert the toggle state if the action failed
      setCombinations(combinations.map(combo =>
        combo.name === combinationName ? { ...combo, enabled: !isOn } : combo
      ));
    }
  };

  // You might need a way to fetch the initial state of combinations from the backend
  // useEffect(() => {
  //   const fetchCombinationStatus = async () => {
  //     // Implement a backend endpoint to get the status of combinations
  //     // const response = await fetch('/api/combination_status');
  //     // if (response.ok) {
  //     //   const statusData = await response.json();
  //     //   setCombinations(combinations.map(combo => ({
  //     //     ...combo,
  //     //     enabled: statusData[combo.name] || false, // Assuming backend returns status by name
  //     //   })));
  //     // }
  //   };
  //   fetchCombinationStatus();
  // }, []); // Run once on component mount

  // Placeholder for fetching logs related to a combination (more complex)
  // const fetchLogsForCombination = async (combinationName) => {
  //   // This would require backend support to aggregate logs for a combination
  //   setLogs(`Fetching logs for ${combinationName}... (Not yet implemented)`);
  // };


  return (
    <div className="App">
      <header className="App-header">
        <img src={logo} className="App-logo" alt="logo" />
        <p>
          Harbor Combination Control
        </p>
      </header>
      <div className="container-fluid">
        <div className="row mb-3">
          <div className="col-md-12">
            <h3>Control Combinations</h3>
            {combinations.map((combo) => (
              <div key={combo.name} className="form-check form-switch d-flex align-items-center mb-2">
                <SwitchButton
                  checked={combo.enabled}
                  onLabel='ON'
                  offLabel='OFF'
                  onChange={(isOn) => handleCombinationToggle(combo.name, isOn)}
                />
                <label className="form-check-label ms-3">{combo.name}</label>
              </div>
            ))}
          </div>
        </div>
        {/* Keep logs area, maybe repurpose later for combination logs */}
        <div className="row">
          <div className="col-md-12">
             <h3>Logs</h3>
            <textarea className="form-control" rows="10" value={logs} readOnly placeholder="Combination logs will appear here..." />
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
