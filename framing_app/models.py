from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class GlobalSetting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Float, nullable=False)
    description = db.Column(db.String(200))

class Moulding(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    pid = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.String(100), nullable=False)
    material = db.Column(db.String(50))
    width_in = db.Column(db.Float, nullable=False)
    wholesale_lf = db.Column(db.Float, nullable=False)
    markup = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(50))
    @property
    def retail_lf(self): return self.wholesale_lf * (1 + self.markup)

class Glazing(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    gid = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50))
    wholesale_sqft = db.Column(db.Float, nullable=False)
    markup = db.Column(db.Float, nullable=False)
    @property
    def retail_sqft(self): return self.wholesale_sqft * (1 + self.markup)

class Matboard(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    mid = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.String(100), nullable=False)
    quality = db.Column(db.String(50))
    wholesale_sqft = db.Column(db.Float, nullable=False)
    markup = db.Column(db.Float, nullable=False)
    @property
    def retail_sqft(self): return self.wholesale_sqft * (1 + self.markup)

class HistoricalData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    month = db.Column(db.String(10), nullable=False)
    margin_percent = db.Column(db.Float, nullable=False)