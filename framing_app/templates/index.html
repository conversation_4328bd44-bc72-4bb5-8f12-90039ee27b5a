{% extends "layout.html" %}
{% block content %}
<div class="content-block"><h1>Custom Framing Job Calculator</h1><p>Enter artwork dimensions and select materials to calculate the price.</p></div>
<div class="calculator-grid">
    <div class="card">
        <h2>Job Inputs</h2>
        <form action="{{ url_for('job_calculator') }}" method="post">
            <div class="form-group"><label for="width">Artwork Width (in)</label><input type="number" name="width" step="0.01" value="{{ results.width if results else 16 }}" required></div>
            <div class="form-group"><label for="height">Artwork Height (in)</label><input type="number" name="height" step="0.01" value="{{ results.height if results else 20 }}" required></div>
            <div class="form-group"><label for="moulding_id">Moulding</label><select name="moulding_id" required>{% for m in mouldings %}<option value="{{ m.id }}" {% if results and results.moulding.id == m.id %}selected{% endif %}>{{ m.pid }} - {{ m.description }}</option>{% endfor %}</select></div>
            <div class="form-group"><label for="glazing_id">Glazing</label><select name="glazing_id" required>{% for g in glazings %}<option value="{{ g.id }}" {% if results and results.glazing.id == g.id %}selected{% endif %}>{{ g.gid }} - {{ g.description }}</option>{% endfor %}</select></div>
            <div class="form-group"><label for="matboard_id">Matboard</label><select name="matboard_id" required>{% for mb in matboards %}<option value="{{ mb.id }}" {% if results and results.matboard.id == mb.id %}selected{% endif %}>{{ mb.mid }} - {{ mb.description }}</option>{% endfor %}</select></div>
            <button type="submit" class="btn">Calculate Price</button>
        </form>
    </div>
    {% if results %}
    <div class="card">
        <h2>Calculation Results</h2>
        <div class="results-summary"><h3>Final Price: <span class="final-price">{{ results.final_price | currency }}</span></h3><h4>Gross Margin: {{ "%.1f"|format(results.gross_margin) }}%</h4></div>
        <div class="details-grid">
            <div class="detail-item"><span class="label">Moulding Cost</span><span class="value">{{ results.moulding_cost | currency }}</span></div>
<div class="detail-item"><span class="label">Glazing Cost</span><span class="value">{{ results.glazing_cost | currency }}</span></div>
<div class="detail-item"><span class="label">Matboard Cost</span><span class="value">{{ results.matboard_cost | currency }}</span></div>
<div class="detail-item"><span class="label">Labor ({{ "%.2f"|format(results.labor_time_hr) }} hrs)</span><span class="value">{{ results.labor_cost | currency }}</span></div>
            <div class="detail-item"><span class="label">Hardware & Misc</span><span class="value">{{ results.hardware_cost | currency }}</span></div>
            <div class="detail-item subtotal"><span class="label">Subtotal</span><span class="value">{{ results.subtotal | currency }}</span></div>
            <div class="detail-item"><span class="label">Overhead ({{ "%.0f"|format(settings.overhead_factor.value*100) }}%)</span><span class="value">{{ results.overhead_amount | currency }}</span></div>
            <hr><div class="detail-item"><span class="label">Gross Profit</span><span class="value">{{ results.gross_profit | currency }}</span></div>
        </div>
    </div>
    {% else %}<div class="card placeholder"><h2>Results will appear here</h2><p>Fill out the form to see the job quote.</p></div>{% endif %}
</div>
{% endblock %}
