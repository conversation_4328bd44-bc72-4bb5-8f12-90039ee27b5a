# --- START OF FILE: framing_pricing_app/framing_app/app.py (Corrected) ---

from flask import Flask, render_template, request, redirect, url_for, flash
from models import db, GlobalSetting, Moulding, Glazing, Matboard, HistoricalData
import os


app = Flask(__name__,
            static_folder='static',
            template_folder='templates')
app.config['SECRET_KEY'] = 'a_very_secret_key'
# Use an absolute path for the database to avoid issues
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'framing_pricing.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)

# ... (rest of the file is the same) ...

# Helper function to get settings as a dictionary
def get_settings():
    settings_list = GlobalSetting.query.all()
    return {s.key: {'value': s.value, 'description': s.description} for s in settings_list}

@app.context_processor
def inject_settings():
    # Makes settings available to all templates
    return {'settings': get_settings()}

# Custom Jinja filter for currency formatting
@app.template_filter('currency')
def currency_filter(value):
    if value is None:
        return "$0.00"
    return f"${value:,.2f}"

@app.route('/', methods=['GET', 'POST'])
def job_calculator():
    mouldings = Moulding.query.order_by(Moulding.pid).all()
    glazings = Glazing.query.order_by(Glazing.gid).all()
    matboards = Matboard.query.order_by(Matboard.mid).all()
    
    results = None
    if request.method == 'POST':
        try:
            # --- INPUTS ---
            width = float(request.form.get('width'))
            height = float(request.form.get('height'))
            moulding_id = int(request.form.get('moulding_id'))
            glazing_id = int(request.form.get('glazing_id'))
            matboard_id = int(request.form.get('matboard_id'))
            
            settings_dict = {k: v['value'] for k, v in get_settings().items()}
            moulding = Moulding.query.get(moulding_id)
            glazing = Glazing.query.get(glazing_id)
            matboard = Matboard.query.get(matboard_id)

            # --- CALCULATIONS (from Excel logic) ---
            # Frame
            perimeter_in = 2 * (width + height)
            linear_feet = perimeter_in / 12
            linear_feet_waste = linear_feet + (settings_dict.get('waste_factor_in', 8) / 12)
            moulding_cost = linear_feet_waste * moulding.retail_lf
            
            # Area
            area_sqin = width * height
            area_sqft = area_sqin / 144
            glazing_cost = area_sqft * glazing.retail_sqft
            matboard_cost = area_sqft * matboard.retail_sqft
            
            # Labor
            labor_time_hr = 0.5 + (area_sqft * 0.25) # Formula from Excel
            labor_cost = labor_time_hr * settings_dict.get('labor_rate_hr', 45)
            
            # Other
            hardware_cost = settings_dict.get('hardware_misc_cost', 15)
            
            # --- PRICING SUMMARY ---
            subtotal = moulding_cost + glazing_cost + matboard_cost + labor_cost + hardware_cost
            overhead_amount = subtotal * settings_dict.get('overhead_factor', 0.15)
            total_before_min = subtotal + overhead_amount
            final_price = max(total_before_min, settings_dict.get('minimum_charge', 75))
            
            # --- COST BREAKDOWN ANALYSIS ---
            wholesale_moulding = linear_feet_waste * moulding.wholesale_lf
            wholesale_glazing = area_sqft * glazing.wholesale_sqft
            wholesale_matboard = area_sqft * matboard.wholesale_sqft
            total_wholesale = wholesale_moulding + wholesale_glazing + wholesale_matboard + labor_cost + hardware_cost
            
            gross_profit = final_price - total_wholesale
            gross_margin = (gross_profit / final_price) * 100 if final_price > 0 else 0
            
            results = {
                'width': width, 'height': height,
                'moulding': moulding, 'glazing': glazing, 'matboard': matboard,
                'linear_feet_waste': linear_feet_waste,
                'area_sqft': area_sqft,
                'labor_time_hr': labor_time_hr,
                'moulding_cost': moulding_cost,
                'glazing_cost': glazing_cost,
                'matboard_cost': matboard_cost,
                'labor_cost': labor_cost,
                'hardware_cost': hardware_cost,
                'subtotal': subtotal,
                'overhead_amount': overhead_amount,
                'final_price': final_price,
                'total_wholesale': total_wholesale,
                'gross_profit': gross_profit,
                'gross_margin': gross_margin
            }
        except (ValueError, TypeError) as e:
            flash(f"Error in calculation. Please check your inputs. Details: {e}", "danger")

    return render_template('index.html', mouldings=mouldings, glazings=glazings, matboards=matboards, results=results)

@app.route('/api/job_calculator', methods=['POST'])
def api_job_calculator():
    try:
        # --- INPUTS ---
        width = float(request.form.get('width'))
        height = float(request.form.get('height'))
        moulding_id = int(request.form.get('moulding_id'))
        glazing_id = int(request.form.get('glazing_id'))
        matboard_id = int(request.form.get('matboard_id'))

        settings_dict = {k: v['value'] for k, v in get_settings().items()}
        moulding = Moulding.query.get(moulding_id)
        glazing = Glazing.query.get(glazing_id)
        matboard = Matboard.query.get(matboard_id)

        # --- CALCULATIONS (from Excel logic) ---
        # Frame
        perimeter_in = 2 * (width + height)
        linear_feet = perimeter_in / 12
        linear_feet_waste = linear_feet + (settings_dict.get('waste_factor_in', 8) / 12)
        moulding_cost = linear_feet_waste * moulding.retail_lf

        # Area
        area_sqin = width * height
        area_sqft = area_sqin / 144
        glazing_cost = area_sqft * glazing.retail_sqft
        matboard_cost = area_sqft * matboard.retail_sqft

        # Labor
        labor_time_hr = 0.5 + (area_sqft * 0.25)  # Formula from Excel
        labor_cost = labor_time_hr * settings_dict.get('labor_rate_hr', 45)

        # Other
        hardware_cost = settings_dict.get('hardware_misc_cost', 15)

        # --- PRICING SUMMARY ---
        subtotal = moulding_cost + glazing_cost + matboard_cost + labor_cost + hardware_cost
        overhead_amount = subtotal * settings_dict.get('overhead_factor', 0.15)
        total_before_min = subtotal + overhead_amount
        final_price = max(total_before_min, settings_dict.get('minimum_charge', 75))

        # --- COST BREAKDOWN ANALYSIS ---
        wholesale_moulding = linear_feet_waste * moulding.wholesale_lf
        wholesale_glazing = area_sqft * glazing.wholesale_sqft
        wholesale_matboard = area_sqft * matboard.wholesale_sqft
        total_wholesale = wholesale_moulding + wholesale_glazing + wholesale_matboard + labor_cost + hardware_cost

        gross_profit = final_price - total_wholesale
        gross_margin = (gross_profit / final_price) * 100 if final_price > 0 else 0

        results = {
            'width': width, 'height': height,
            'moulding': moulding.description, 'glazing': glazing.description, 'matboard': matboard.description,
            'linear_feet_waste': linear_feet_waste,
            'area_sqft': area_sqft,
            'labor_time_hr': labor_time_hr,
            'moulding_cost': moulding_cost,
            'glazing_cost': glazing_cost,
            'matboard_cost': matboard_cost,
            'labor_cost': labor_cost,
            'hardware_cost': hardware_cost,
            'subtotal': subtotal,
            'overhead_amount': overhead_amount,
            'final_price': final_price,
            'total_wholesale': total_wholesale,
            'gross_profit': gross_profit,
            'gross_margin': gross_margin
        }
        return jsonify(results)
    except (ValueError, TypeError) as e:
        return jsonify({'error': str(e)}), 400

from flask import jsonify

@app.route('/api/inventory', methods=['GET'])
def api_inventory():
    mouldings = Moulding.query.all()
    glazings = Glazing.query.all()
    matboards = Matboard.query.all()
    
    mouldings_list = [{'id': m.id, 'pid': m.pid, 'description': m.description, 'material': m.material, 'width_in': m.width_in, 'wholesale_lf': m.wholesale_lf, 'markup': m.markup, 'category': m.category} for m in mouldings]
    glazings_list = [{'id': g.id, 'gid': g.gid, 'description': g.description, 'type': g.type, 'wholesale_sqft': g.wholesale_sqft, 'markup': g.markup} for g in glazings]
    matboards_list = [{'id': mat.id, 'mid': mat.mid, 'description': mat.description, 'quality': mat.quality, 'wholesale_sqft': mat.wholesale_sqft, 'markup': mat.markup} for mat in matboards]

    return jsonify({'mouldings': mouldings_list, 'glazings': glazings_list, 'matboards': matboards_list})

@app.route('/api/global_settings', methods=['GET', 'POST'])
def api_global_settings():
    if request.method == 'GET':
        settings = GlobalSetting.query.all()
        settings_list = [{'id': s.id, 'key': s.key, 'value': s.value, 'description': s.description} for s in settings]
        return jsonify(settings_list)
    elif request.method == 'POST':
        try:
            data = request.get_json()
            for item in data:
                setting = GlobalSetting.query.get(item['id'])
                if setting:
                    setting.value = float(item['value'])
            db.session.commit()
            return jsonify({'message': 'Global settings updated successfully!'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

@app.route('/api/historical_data', methods=['GET'])
def api_historical_data():
    historical_data = HistoricalData.query.all()
    historical_data_list = [{'id': h.id, 'month': h.month, 'margin_percent': h.margin_percent} for h in historical_data]
    return jsonify(historical_data_list)

@app.route('/api/pid_controls', methods=['POST'])
def api_pid_controls():
    try:
        # Get inputs
        kp = float(request.form.get('kp', 1.0))
        ki = float(request.form.get('ki', 0.1))
        kd = float(request.form.get('kd', 0.05))
        setpoint = float(request.form.get('setpoint', 0.5))

        # Get historical data
        hist_data = HistoricalData.query.all()
        margins = [h.margin_percent for h in hist_data]
        
        # Current margin - run a sample calculation (16x20 basic frame)
        m001 = Moulding.query.filter_by(pid="M001").first()
        g001 = Glazing.query.filter_by(gid="G001").first()
        mat001 = Matboard.query.filter_by(mid="MAT001").first()
        settings_dict = {k: v['value'] for k, v in get_settings().items()}
        
        # Simplified calc for margin
        lf_waste = (2*(16+20)/12) + (settings_dict.get('waste_factor_in', 8)/12)
        sqft = (16*20)/144
        labor_hr = 0.5 + (sqft * 0.25)
        
        retail_subtotal = (lf_waste * m001.retail_lf) + (sqft * g001.retail_sqft) + (sqft * mat001.retail_sqft) + (labor_hr * settings_dict.get('labor_rate_hr', 45)) + settings_dict.get('hardware_misc_cost', 15)
        retail_price = retail_subtotal * (1 + settings_dict.get('overhead_factor', 0.15))
        
        wholesale_price = (lf_waste * m001.wholesale_lf) + (sqft * g001.wholesale_sqft) + (sqft * mat001.wholesale_sqft) + (labor_hr * settings_dict.get('labor_rate_hr', 45)) + settings_dict.get('hardware_misc_cost', 15)

        current_margin = (retail_price - wholesale_price) / retail_price if retail_price > 0 else 0
        
        # PID Calculations
        error = setpoint - current_margin
        p_component = kp * error
        
        historical_avg_margin = sum(margins) / len(margins) if margins else 0
        i_component = ki * (historical_avg_margin - setpoint)
        
        rate_of_change = (margins[-1] - margins[-2]) if len(margins) >= 2 else 0
        d_component = kd * rate_of_change
        
        pid_output = p_component + i_component + d_component
        
        results = {
            'current_margin': current_margin,
            'error': error,
            'p_component': p_component,
            'i_component': i_component,
            'd_component': d_component,
            'pid_output': pid_output,
            'recommended_adjustment': pid_output * 100
        }
        return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/inventory', methods=['GET', 'POST'])
def inventory():
    if request.method == 'POST':
        # This handles updates to Global Settings
        settings = GlobalSetting.query.all()
        for s in settings:
            new_value = request.form.get(s.key)
            if new_value is not None:
                s.value = float(new_value)
        db.session.commit()
        flash("Global settings updated successfully!", "success")
        return redirect(url_for('inventory'))

    mouldings = Moulding.query.order_by(Moulding.pid).all()
    glazings = Glazing.query.order_by(Glazing.gid).all()
    matboards = Matboard.query.order_by(Matboard.mid).all()
    
    return render_template('inventory.html', mouldings=mouldings, glazings=glazings, matboards=matboards)

@app.route('/pid_controls', methods=['GET', 'POST'])
def pid_controls():
    results = None
    if request.method == 'POST':
        # Get inputs
        kp = float(request.form.get('kp', 1.0))
        ki = float(request.form.get('ki', 0.1))
        kd = float(request.form.get('kd', 0.05))
        setpoint = float(request.form.get('setpoint', 0.5))

        # Get historical data
        hist_data = HistoricalData.query.all()
        margins = [h.margin_percent for h in hist_data]
        
        # Current margin - run a sample calculation (16x20 basic frame)
        m001 = Moulding.query.filter_by(pid="M001").first()
        g001 = Glazing.query.filter_by(gid="G001").first()
        mat001 = Matboard.query.filter_by(mid="MAT001").first()
        settings_dict = {k: v['value'] for k, v in get_settings().items()}
        
        # Simplified calc for margin
        lf_waste = (2*(16+20)/12) + (settings_dict.get('waste_factor_in', 8)/12)
        sqft = (16*20)/144
        labor_hr = 0.5 + (sqft * 0.25)
        
        retail_subtotal = (lf_waste * m001.retail_lf) + (sqft * g001.retail_sqft) + (sqft * mat001.retail_sqft) + (labor_hr * settings_dict.get('labor_rate_hr', 45)) + settings_dict.get('hardware_misc_cost', 15)
        retail_price = retail_subtotal * (1 + settings_dict.get('overhead_factor', 0.15))
        
        wholesale_price = (lf_waste * m001.wholesale_lf) + (sqft * g001.wholesale_sqft) + (sqft * mat001.wholesale_sqft) + (labor_hr * settings_dict.get('labor_rate_hr', 45)) + settings_dict.get('hardware_misc_cost', 15)

        current_margin = (retail_price - wholesale_price) / retail_price if retail_price > 0 else 0
        
        # PID Calculations
        error = setpoint - current_margin
        p_component = kp * error
        
        historical_avg_margin = sum(margins) / len(margins) if margins else 0
        i_component = ki * (historical_avg_margin - setpoint)
        
        rate_of_change = (margins[-1] - margins[-2]) if len(margins) >= 2 else 0
        d_component = kd * rate_of_change
        
        pid_output = p_component + i_component + d_component
        
        results = {
            'current_margin': current_margin,
            'error': error,
            'p_component': p_component,
            'i_component': i_component,
            'd_component': d_component,
            'pid_output': pid_output,
            'recommended_adjustment': pid_output * 100
        }
    
    return render_template('pid_controls.html', results=results)


if __name__ == '__main__':
    # Ensure the instance folder exists for the db
    instance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..')
    if not os.path.exists(os.path.join(instance_path, 'framing_pricing.db')):
        print("Database not found. Please run the install script first.")
    else:
        app.run(host='0.0.0.0', port=5001, debug=True)

# --- END OF FILE ---
