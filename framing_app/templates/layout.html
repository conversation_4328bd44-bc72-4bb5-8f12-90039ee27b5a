<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Framing Pricing System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <nav class="navbar"><div class="container">
        <a href="{{ url_for('job_calculator') }}" class="nav-brand">Framing Pricing System</a>
        <ul class="nav-links"><li><a href="{{ url_for('job_calculator') }}">Job Calculator</a></li><li><a href="{{ url_for('inventory') }}">Inventory & Settings</a></li></ul>
    </div></nav>
    <main class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}{% for category, message in messages %}<div class="alert alert-{{ category }}">{{ message }}</div>{% endfor %}{% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </main>
    <footer><div class="container"><p>© 2025 Custom Framing Pricing System.</p></div></footer>
</body>
</html>