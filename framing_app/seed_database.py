# --- START OF FILE: framing_pricing_app/framing_app/seed_database.py (Corrected) ---

from app import app
from models import db, GlobalSetting, Moulding, Glazing, Matboard, HistoricalData
import random

def seed_data():
    with app.app_context():
        db.drop_all()
        db.create_all()

        # ... (rest of the file is the same) ...
        # Global Settings
        settings_data = [
            {'key': 'labor_rate_hr', 'value': 45.00, 'description': 'Labor Rate ($/hour)'},
            {'key': 'overhead_factor', 'value': 0.15, 'description': 'Overhead Factor (%)'},
            {'key': 'minimum_charge', 'value': 75.00, 'description': 'Minimum Job Charge ($)'},
            {'key': 'waste_factor_in', 'value': 8.0, 'description': 'Waste Factor (inches)'},
            {'key': 'default_markup', 'value': 2.5, 'description': 'Default Markup (%)'},
            {'key': 'hardware_misc_cost', 'value': 15.00, 'description': 'Hardware & Misc Job Cost ($)'}
        ]
        for s in settings_data:
            db.session.add(GlobalSetting(**s))

        # Moulding Data
        moulding_data = [
            {"pid": "M001", "description": "Basic Wood Frame", "material": "Pine", "width_in": 1.5, "wholesale_lf": 8.50, "markup": 2.5, "category": "Basic"},
            {"pid": "M002", "description": "Premium Oak Frame", "material": "Oak", "width_in": 2.0, "wholesale_lf": 15.75, "markup": 3.0, "category": "Premium"},
            {"pid": "M003", "description": "Metal Silver Frame", "material": "Aluminum", "width_in": 1.0, "wholesale_lf": 12.25, "markup": 2.8, "category": "Metal"},
            {"pid": "M004", "description": "Ornate Gold Frame", "material": "Wood/Gold", "width_in": 3.0, "wholesale_lf": 28.50, "markup": 3.5, "category": "Ornate"},
            {"pid": "M005", "description": "Modern Black Frame", "material": "Composite", "width_in": 1.25, "wholesale_lf": 9.75, "markup": 2.6, "category": "Modern"},
            {"pid": "M006", "description": "Rustic Barnwood", "material": "Reclaimed Wood", "width_in": 2.5, "wholesale_lf": 22.00, "markup": 3.2, "category": "Rustic"},
            {"pid": "M007", "description": "Museum Quality", "material": "Hardwood", "width_in": 2.0, "wholesale_lf": 35.00, "markup": 2.0, "category": "Museum"},
            {"pid": "M008", "description": "Budget Plastic", "material": "Plastic", "width_in": 1.0, "wholesale_lf": 4.25, "markup": 4.0, "category": "Budget"}
        ]
        for m in moulding_data:
            db.session.add(Moulding(**m))

        # Glazing Data
        glazing_data = [
            {"gid": "G001", "description": "Regular Glass", "type": "Glass", "wholesale_sqft": 3.50, "markup": 2.5},
            {"gid": "G002", "description": "Non-Glare Glass", "type": "Glass", "wholesale_sqft": 8.75, "markup": 2.2},
            {"gid": "G003", "description": "UV Protection Glass", "type": "Glass", "wholesale_sqft": 12.50, "markup": 2.0},
            {"gid": "G004", "description": "Museum Glass", "type": "Glass", "wholesale_sqft": 25.00, "markup": 1.8},
            {"gid": "G005", "description": "Acrylic Standard", "type": "Acrylic", "wholesale_sqft": 4.25, "markup": 2.3},
            {"gid": "G006", "description": "Acrylic UV", "type": "Acrylic", "wholesale_sqft": 9.50, "markup": 2.1}
        ]
        for g in glazing_data:
            db.session.add(Glazing(**g))

        # Matboard Data
        matboard_data = [
            {"mid": "MAT001", "description": "Standard Mat", "quality": "Regular", "wholesale_sqft": 2.25, "markup": 3.0},
            {"mid": "MAT002", "description": "Acid-Free Mat", "quality": "Conservation", "wholesale_sqft": 4.50, "markup": 2.5},
            {"mid": "MAT003", "description": "Museum Mat", "quality": "Museum", "wholesale_sqft": 8.75, "markup": 2.0},
            {"mid": "MAT004", "description": "Fabric Mat", "quality": "Specialty", "wholesale_sqft": 12.00, "markup": 2.2},
            {"mid": "MAT005", "description": "Double Mat", "quality": "Premium", "wholesale_sqft": 6.25, "markup": 2.3}
        ]
        for m in matboard_data:
            db.session.add(Matboard(**m))

        # Historical Data for PID
        months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        for month in months:
            margin = 0.45 + random.randint(-5, 10) / 100
            db.session.add(HistoricalData(month=month, margin_percent=margin))

        db.session.commit()
        print("Database seeded successfully!")

if __name__ == '__main__':
    seed_data()

# --- END OF FILE ---
