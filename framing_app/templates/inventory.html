{% extends "layout.html" %}
{% block content %}
<div class="content-block"><h1>Inventory & Global Settings</h1><p>Manage materials and global pricing variables.</p></div>
<div class="card">
    <h2>Global Settings</h2>
    <form action="{{ url_for('inventory') }}" method="post">
        <div class="settings-grid">
        {% for key, setting in settings.items() %}
            <div class="form-group"><label for="{{ key }}">{{ setting.description }}</label><input type="number" step="0.01" name="{{ key }}" value="{{ setting.value }}"></div>
        {% endfor %}
        </div>
        <button type="submit" class="btn">Save Settings</button>
    </form>
</div>
<div class="card"><h2>Moulding Inventory</h2><table><thead><tr><th>PID</th><th>Description</th><th>Wholesale $/LF</th><th>Markup</th><th>Retail $/LF</th></tr></thead><tbody>
{% for m in mouldings %}<tr><td>{{ m.pid }}</td><td>{{ m.description }}</td><td>{{ m.wholesale_lf | currency }}</td><td>{{ "%.0f"|format(m.markup * 100) }}%</td><td>{{ m.retail_lf | currency }}</td></tr>{% endfor %}
</tbody></table></div>
<div class="card"><h2>Glazing Inventory</h2><table><thead><tr><th>GID</th><th>Description</th><th>Wholesale $/SqFt</th><th>Markup</th><th>Retail $/SqFt</th></tr></thead><tbody>
{% for g in glazings %}<tr><td>{{ g.gid }}</td><td>{{ g.description }}</td><td>{{ g.wholesale_sqft | currency }}</td><td>{{ "%.0f"|format(g.markup * 100) }}%</td><td>{{ g.retail_sqft | currency }}</td></tr>{% endfor %}
</tbody></table></div>
<div class="card"><h2>Matboard Inventory</h2><table><thead><tr><th>MID</th><th>Description</th><th>Wholesale $/SqFt</th><th>Markup</th><th>Retail $/SqFt</th></tr></thead><tbody>
{% for m in matboards %}<tr><td>{{ m.mid }}</td><td>{{ m.description }}</td><td>{{ m.wholesale_sqft | currency }}</td><td>{{ "%.0f"|format(m.markup * 100) }}%</td><td>{{ m.retail_sqft | currency }}</td></tr>{% endfor %}
</tbody></table></div>
{% endblock %}